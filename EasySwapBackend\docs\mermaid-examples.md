# NFT项目Mermaid图表示例

## 1. 项目启动流程

```mermaid
graph TD
    A[main.go启动] --> B[解析命令行参数]
    B --> C[读取config.toml]
    C --> D{配置文件有效?}
    D -->|否| E[panic退出]
    D -->|是| F[验证链配置]
    F --> G{链配置有效?}
    G -->|否| H[panic: invalid chain_suffix config]
    G -->|是| I[创建ServiceContext]
    I --> J[初始化Redis连接]
    I --> K[初始化MySQL连接]
    I --> L[初始化区块链客户端]
    I --> M[初始化日志服务]
    J --> N[创建Router]
    K --> N
    L --> N
    M --> N
    N --> O[创建Platform]
    O --> P[启动服务 app.Start]
    
    style A fill:#e1f5fe
    style P fill:#c8e6c9
    style E fill:#ffcdd2
    style H fill:#ffcdd2
```

## 2. NFT交易流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as API服务
    participant Auth as 认证服务
    participant DB as MySQL
    participant Cache as Redis
    participant BC as 区块链
    
    U->>API: 购买NFT请求
    API->>Auth: 验证用户身份
    Auth-->>API: 身份验证结果
    
    alt 身份验证成功
        API->>Cache: 检查NFT缓存
        Cache-->>API: 返回NFT信息
        
        alt NFT可购买
            API->>DB: 开始事务
            API->>BC: 调用智能合约
            BC-->>API: 交易哈希
            API->>DB: 更新所有权
            API->>DB: 提交事务
            API->>Cache: 更新缓存
            API-->>U: 购买成功
        else NFT不可购买
            API-->>U: 购买失败
        end
    else 身份验证失败
        API-->>U: 认证失败
    end
```

## 3. 系统架构图

```mermaid
graph TB
    subgraph "客户端层"
        Web[Web前端]
        Mobile[移动端]
        API_Client[API客户端]
    end
    
    subgraph "网关层"
        LB[负载均衡器]
        Gateway[API网关]
    end
    
    subgraph "应用层"
        App1[应用服务器1]
        App2[应用服务器2]
        App3[应用服务器3]
    end
    
    subgraph "服务层"
        UserSvc[用户服务]
        NFTSvc[NFT服务]
        CollectionSvc[集合服务]
        TradeSvc[交易服务]
    end
    
    subgraph "数据层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        IPFS[(IPFS存储)]
    end
    
    subgraph "区块链层"
        ETH[以太坊]
        BSC[BSC链]
        Polygon[Polygon链]
    end
    
    Web --> LB
    Mobile --> LB
    API_Client --> LB
    
    LB --> Gateway
    Gateway --> App1
    Gateway --> App2
    Gateway --> App3
    
    App1 --> UserSvc
    App1 --> NFTSvc
    App2 --> CollectionSvc
    App2 --> TradeSvc
    App3 --> UserSvc
    App3 --> NFTSvc
    
    UserSvc --> MySQL
    NFTSvc --> MySQL
    CollectionSvc --> MySQL
    TradeSvc --> MySQL
    
    UserSvc --> Redis
    NFTSvc --> Redis
    CollectionSvc --> Redis
    TradeSvc --> Redis
    
    NFTSvc --> IPFS
    CollectionSvc --> IPFS
    
    TradeSvc --> ETH
    TradeSvc --> BSC
    TradeSvc --> Polygon
```

## 4. 数据库关系图

```mermaid
erDiagram
    USERS {
        string id PK
        string wallet_address
        string username
        string email
        timestamp created_at
        timestamp updated_at
    }
    
    COLLECTIONS {
        string id PK
        string name
        string description
        string owner_id FK
        int64 view_count
        timestamp created_at
        timestamp updated_at
    }
    
    NFTS {
        string id PK
        string collection_id FK
        string owner_id FK
        string token_uri
        string metadata
        decimal price
        string status
        timestamp created_at
        timestamp updated_at
    }
    
    TRANSACTIONS {
        string id PK
        string nft_id FK
        string from_user_id FK
        string to_user_id FK
        decimal amount
        string tx_hash
        string status
        timestamp created_at
    }
    
    USERS ||--o{ COLLECTIONS : owns
    USERS ||--o{ NFTS : owns
    COLLECTIONS ||--o{ NFTS : contains
    USERS ||--o{ TRANSACTIONS : participates
    NFTS ||--o{ TRANSACTIONS : involves
```

## 5. NFT状态流转图

```mermaid
stateDiagram-v2
    [*] --> Created : 创建NFT
    Created --> Minting : 开始铸造
    Minting --> Minted : 铸造成功
    Minting --> Failed : 铸造失败
    Failed --> [*] : 删除记录
    
    Minted --> ForSale : 上架销售
    ForSale --> Sold : 购买成功
    ForSale --> Cancelled : 取消销售
    
    Sold --> ForSale : 重新上架
    Cancelled --> ForSale : 重新上架
    
    Sold --> Transferred : 转移所有权
    Transferred --> Minted : 转移完成
```

## 6. 缓存策略图

```mermaid
graph TD
    A[用户请求] --> B{检查Redis缓存}
    B -->|命中| C[返回缓存数据]
    B -->|未命中| D[查询MySQL]
    D --> E[获取数据]
    E --> F[写入Redis缓存]
    F --> G[返回数据给用户]
    
    H[数据更新] --> I[更新MySQL]
    I --> J[删除Redis缓存]
    J --> K[下次请求重新缓存]
    
    style C fill:#c8e6c9
    style G fill:#c8e6c9
```

## 7. 分布式锁流程

```mermaid
sequenceDiagram
    participant S1 as 服务器1
    participant S2 as 服务器2
    participant R as Redis
    participant DB as MySQL
    
    Note over S1,S2: 同时处理NFT购买请求
    
    S1->>R: SetNX(lock_key, server1, 30s)
    S2->>R: SetNX(lock_key, server2, 30s)
    
    R-->>S1: 成功获取锁
    R-->>S2: 获取锁失败
    
    S2-->>S2: 返回"正在处理中"
    
    S1->>DB: 执行购买逻辑
    DB-->>S1: 购买成功
    
    S1->>R: DEL(lock_key)
    R-->>S1: 锁释放成功
    
    S1-->>S1: 返回"购买成功"
```

## 使用说明

1. 复制上述代码到 `.md` 文件中
2. 在VSCode中安装Mermaid插件
3. 按 `Ctrl+Shift+V` 预览效果
4. 可以根据实际情况修改图表内容
