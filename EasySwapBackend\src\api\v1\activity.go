package v1

import (
	"encoding/json"

	"github.com/ProjectsTask/EasySwapBase/errcode"
	"github.com/ProjectsTask/EasySwapBase/xhttp"
	"github.com/gin-gonic/gin"

	"github.com/ProjectsTask/EasySwapBackend/src/service/svc"
	"github.com/ProjectsTask/EasySwapBackend/src/service/v1"
	"github.com/ProjectsTask/EasySwapBackend/src/types/v1"
)

// ActivityMultiChainHandler 处理多链活动查询请求
// 主要功能:
// 1. 解析过滤参数
// 2. 根据是否指定链ID执行不同的查询逻辑:
//   - 未指定链ID: 查询所有链上的活动
//   - 指定链ID: 只查询指定链上的活动
func ActivityMultiChainHandler(svcCtx *svc.ServerCtx) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取过滤参数
		filterParam := c.Query("filters") // 从查询参数中获取过滤参数
		if filterParam == "" {
			xhttp.Error(c, errcode.NewCustomErr("Filter param is nil."))
			return
		}

		// 解析过滤参数
		var filter types.ActivityMultiChainFilterParams
		err := json.Unmarshal([]byte(filterParam), &filter)
		if err != nil {
			xhttp.Error(c, errcode.NewCustomErr("Filter param is nil."))
			return
		}

		// 指定链ID,只查询指定链上的活动
		var chainName []string
		for _, id := range filter.ChainID { //遍历链ID
			chainName = append(chainName, chainIDToChain[id])
		}

		res, err := service.GetMultiChainActivities(
			c.Request.Context(),
			svcCtx,
			filter.ChainID,
			chainName,                  //链名称
			filter.CollectionAddresses, //合约地址
			filter.TokenID,             //tokenID 每个NFT的唯一标识 不同链可能存在相同的tokenID
			filter.UserAddresses,       //用户地址
			filter.EventTypes,          //活动类型 1: mint 2: list 3: cancel 4: buy 5: sell 6: transfer 7: bid 8: cancel bid 9: accept bid 10: transfer bid 11: transfer bid accept 12: transfer bid cancel 13: transfer bid reject 14: transfer bid accept reject 15: transfer bid cancel reject 16: transfer bid reject 17: transfer bid accept cancel 18: transfer bid accept
			filter.Page,                //页码
			filter.PageSize,            //页大小
		)
		if err != nil {
			xhttp.Error(c, errcode.NewCustomErr("Get multi-chain activities failed."))
			return
		}
		xhttp.OkJson(c, res)
	}

}
