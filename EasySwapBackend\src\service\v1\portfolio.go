package service

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"

	"github.com/ProjectsTask/EasySwapBase/stores/gdb/orderbookmodel/multi"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"

	"github.com/ProjectsTask/EasySwapBackend/src/dao"
	"github.com/ProjectsTask/EasySwapBackend/src/service/svc"
	"github.com/ProjectsTask/EasySwapBackend/src/types/v1"
)

const BidTypeOffset = 3

func getBidType(origin int64) int64 {
	if origin >= BidTypeOffset {
		return origin - BidTypeOffset
	} else {
		return origin
	}
}

// GetMultiChainUserCollections 获取用户拥有Collection信息： 拥有item数量、上架数量、floor price
// GetMultiChainUserCollections 获取用户在多条链上的Collection投资组合信息
/*
   功能说明:
   1. 查询用户在多条链上拥有NFT的Collection基本信息(名称、地址、图片等)
   2. 查询用户在每个Collection中拥有的NFT数量
   3. 查询用户在每个Collection中已挂单的NFT数量
   4. 查询每个Collection的市场地板价(用于估算用户资产价值)

   注意: 地板价是Collection的市场地板价，不是用户NFT的挂单价格
*/
func GetMultiChainUserCollections(ctx context.Context, svcCtx *svc.ServerCtx, chainIDs []int, chainNames []string, userAddrs []string) (*types.UserCollectionsResp, error) {
	// 1. 查询用户在多条链上的Collection基本信息
	collections, err := svcCtx.Dao.QueryMultiChainUserCollectionInfos(ctx, chainIDs, chainNames, userAddrs)
	if err != nil {
		return nil, errors.Wrap(err, "failed on get collection info")
	}

	// 2. 构建chainID到chainName的映射
	chainIDToChainName := make(map[int]string)
	for _, chain := range svcCtx.C.ChainSupported {
		chainIDToChainName[chain.ChainID] = chain.Name
	}

	// 3. 构建chainID到Collection地址列表的映射
	chainIDToCollectionAddrs := make(map[int][]string)
	for _, collection := range collections {
		if _, ok := chainIDToCollectionAddrs[collection.ChainID]; !ok {
			chainIDToCollectionAddrs[collection.ChainID] = []string{collection.Address}
		} else {
			chainIDToCollectionAddrs[collection.ChainID] = append(chainIDToCollectionAddrs[collection.ChainID], collection.Address)
		}
	}

	// 4. 并发查询每个Collectionlection的挂单数量
	var listed []types.CollectionInfo
	var wg sync.WaitGroup
	var mu sync.Mutex
	for chainID, collectionAddrs := range chainIDToCollectionAddrs {
		chainName := chainIDToChainName[chainID]
		wg.Add(1)
		go func(chainName string, collectionAddrs []string) {
			defer wg.Done()

			list, err := svcCtx.Dao.QueryListedAmountEachCollection(ctx, chainName, collectionAddrs, userAddrs)
			if err != nil {
				return
			}
			mu.Lock()
			listed = append(listed, list...)
			mu.Unlock()
		}(chainName, collectionAddrs)
	}
	wg.Wait()

	// 5. 构建Collection地址到挂单数量的映射
	collectionsListed := make(map[string]int)
	for _, l := range listed {
		collectionsListed[strings.ToLower(l.Address)] = l.ListAmount
	}

	// 6. 组装最终结果
	var results types.UserCollectionsData
	chainInfos := make(map[int]types.ChainInfo)
	for _, collection := range collections {
		// 6.1 添加Collection信息
		listCount := collectionsListed[strings.ToLower(collection.Address)]
		results.CollectionInfos = append(results.CollectionInfos, types.CollectionInfo{
			ChainID:    collection.ChainID,
			Name:       collection.Name,
			Address:    collection.Address,
			Symbol:     collection.Symbol,
			ImageURI:   collection.ImageURI,
			ListAmount: listCount,
			ItemAmount: collection.ItemCount,
			FloorPrice: collection.FloorPrice,
		})

		// 6.2 计算每条链的统计信息
		chainInfo, ok := chainInfos[collection.ChainID]
		if ok {
			chainInfo.ItemOwned += collection.ItemCount
			chainInfo.ItemValue = chainInfo.ItemValue.Add(decimal.New(collection.ItemCount, 0).Mul(collection.FloorPrice))
			chainInfos[collection.ChainID] = chainInfo
		} else {
			chainInfos[collection.ChainID] = types.ChainInfo{
				ChainID:   collection.ChainID,
				ItemOwned: collection.ItemCount,
				ItemValue: decimal.New(collection.ItemCount, 0).Mul(collection.FloorPrice),
			}
		}
	}

	// 6.3 添加链信息到结果中
	for _, chainInfo := range chainInfos {
		results.ChainInfos = append(results.ChainInfos, chainInfo)
	}

	return &types.UserCollectionsResp{
		Result: results,
	}, nil
}

// GetMultiChainUserItems 查询用户拥有NFT的详细信息，包括：
// 1. Item基本信息：从Item表查询用户拥有的NFT基础数据（合约地址、TokenID、名称等）
// 2. List信息：用户自己的挂单信息，包括挂单价格、订单ID、过期时间等（用户作为卖家的挂单）
// 3. Bid信息：针对该NFT的最高出价信息，包括：
//   - Item级别的最高出价：针对特定NFT的最高出价
//   - Collection级别的最高出价：针对整个集合的最高出价（当没有Item级别出价时使用）
//     注意：这里的bid是其他用户对该NFT的出价，不是用户自己的出价
//
// 4. Collection信息：NFT所属集合的基本信息（名称、地板价、图片等）
// 5. 图片信息：NFT的图片URI信息
func GetMultiChainUserItems(ctx context.Context, svcCtx *svc.ServerCtx, chainID []int, chain []string, userAddrs []string, contractAddrs []string, page, pageSize int) (*types.UserItemsResp, error) {
	// 1. 查询用户拥有的NFT基本信息
	// 从Item表中查询用户在指定链上拥有的NFT基础数据
	items, count, err := svcCtx.Dao.QueryMultiChainUserItemInfos(ctx, chain, userAddrs, contractAddrs, page, pageSize)
	if err != nil {
		return nil, errors.Wrap(err, "failed on get user items info")
	}

	// 如果没有Item,直接返回空结果
	if count == 0 {
		return &types.UserItemsResp{
			Result: items,
			Count:  count,
		}, nil
	}

	// 2. 构建chainID到chain name的映射
	// 用于后续根据chainID快速获取对应的链名称
	chainIDToChainName := make(map[int]string)
	for i, _ := range chainID {
		chainIDToChainName[chainID[i]] = chain[i]
	}

	// 3. 准备查询参数，为后续批量查询做准备
	var collectionAddrs [][]string                          // Collection地址和链名称对，用于查询Collection信息
	var itemInfos []dao.MultiChainItemInfo                  // Item信息，用于查询Item相关数据
	var chainCollections = make(map[string][]string)        // 按链分组的Collection地址，用于并发查询Collection级别的最高出价
	var multichainItems = make(map[string][]types.ItemInfo) // 按链分组的Item信息，用于并发查询Item级别的最高出价

	// 遍历用户拥有的NFT，构建各种查询参数
	for _, item := range items {
		// 为查询Collection信息准备参数
		collectionAddrs = append(collectionAddrs, []string{strings.ToLower(item.CollectionAddress), chainIDToChainName[item.ChainID]})

		// 为查询Item相关信息准备参数
		itemInfos = append(itemInfos, dao.MultiChainItemInfo{
			ItemInfo: types.ItemInfo{
				CollectionAddress: item.CollectionAddress,
				TokenID:           item.TokenID,
			},
			ChainName: chainIDToChainName[item.ChainID],
		})

		// 按链分组Collection地址，用于并发查询Collection级别的最高出价
		chainCollections[strings.ToLower(chainIDToChainName[item.ChainID])] = append(chainCollections[strings.ToLower(chainIDToChainName[item.ChainID])], item.CollectionAddress)

		// 按链分组Item信息，用于并发查询Item级别的最高出价
		multichainItems[chainIDToChainName[item.ChainID]] = append(multichainItems[chainIDToChainName[item.ChainID]], types.ItemInfo{
			CollectionAddress: item.CollectionAddress,
			TokenID:           item.TokenID,
		})
	}

	// 4. 获取用户地址（用于查询出价信息时排除用户自己的出价）
	var userAddr string
	if len(userAddrs) == 0 {
		userAddr = ""
	} else {
		userAddr = userAddrs[0]
	}

	// 5. 并发查询Collection级别的最高出价信息
	// Collection级别的出价是指对整个集合的出价，当没有针对特定NFT的出价时会使用这个价格
	// 注意：这里查询的是其他用户对该Collection的出价，会排除用户自己的出价
	collectionBestBids := make(map[types.MultichainCollection]multi.Order)
	var wg sync.WaitGroup
	var mu sync.Mutex
	var queryErr error
	for chain, collections := range chainCollections {
		wg.Add(1)
		go func(chainName string, collectionArray []string) {
			defer wg.Done()
			// 查询指定链上指定Collection的最高出价，排除用户自己的出价
			bestBids, err := svcCtx.Dao.QueryCollectionsBestBid(ctx, chainName, userAddr, collectionArray)
			if err != nil {
				queryErr = errors.Wrap(err, "failed on query collections best bids")
				return
			}
			mu.Lock()
			defer mu.Unlock()
			// 将查询结果存储到map中，key为Collection地址+链名称
			for _, bestBid := range bestBids {
				collectionBestBids[types.MultichainCollection{
					CollectionAddress: strings.ToLower(bestBid.CollectionAddress),
					Chain:             chainName,
				}] = *bestBid
			}
		}(chain, collections)
	}
	wg.Wait()
	if queryErr != nil {
		return nil, errors.Wrap(err, "failed on query collection bids")
	}

	// 6. 并发查询Item级别的最高出价信息
	// Item级别的出价是指针对特定NFT的出价，优先级高于Collection级别的出价
	// 注意：这里查询的是其他用户对该NFT的出价，会排除用户自己的出价
	itemsBestBids := make(map[dao.MultiChainItemInfo]multi.Order)
	for chain, items := range multichainItems {
		wg.Add(1)
		go func(chainName string, itemInfos []types.ItemInfo) {
			defer wg.Done()
			// 查询指定链上指定NFT的最高出价，排除用户自己的出价
			bids, err := svcCtx.Dao.QueryItemsBestBids(ctx, chainName, userAddr, itemInfos)
			if err != nil {
				queryErr = errors.Wrap(err, "failed on query items best bids")
				return
			}

			mu.Lock()
			defer mu.Unlock()
			// 处理查询结果，如果同一个NFT有多个出价，保留价格最高的
			for _, bid := range bids {
				itemKey := dao.MultiChainItemInfo{ItemInfo: types.ItemInfo{CollectionAddress: strings.ToLower(bid.CollectionAddress), TokenID: bid.TokenId}, ChainName: chainName}
				order, ok := itemsBestBids[itemKey]
				if !ok {
					// 如果该NFT还没有出价记录，直接添加
					itemsBestBids[itemKey] = bid
					continue
				}
				// 如果该NFT已有出价记录，比较价格，保留更高的出价
				if bid.Price.GreaterThan(order.Price) {
					itemsBestBids[itemKey] = bid
				}
			}
		}(chain, items)
	}
	wg.Wait()
	if queryErr != nil {
		return nil, errors.Wrap(err, "failed on query items best bids")
	}

	// 7. 查询Collection基本信息
	// 获取NFT所属集合的基本信息，包括集合名称、地板价、图片等
	collections, err := svcCtx.Dao.QueryMultiChainCollectionsInfo(ctx, collectionAddrs)
	if err != nil {
		return nil, errors.Wrap(err, "failed on query collections info")
	}

	// 将Collection信息存储到map中，便于后续快速查找
	collectionInfos := make(map[string]multi.Collection)
	for _, collection := range collections {
		collectionInfos[strings.ToLower(collection.Address)] = collection
	}

	// 8. 查询用户的NFT挂单信息
	// 这里查询的是用户自己作为卖家的挂单信息，包括挂单价格、是否在售等
	listings, err := svcCtx.Dao.QueryMultiChainUserItemsListInfo(ctx, userAddrs, itemInfos)
	if err != nil {
		return nil, errors.Wrap(err, "failed on query item list info")
	}

	// 将挂单信息存储到map中，key为合约地址+TokenID
	listingInfos := make(map[string]*dao.CollectionItem)
	for _, listing := range listings {
		listingInfos[strings.ToLower(listing.CollectionAddress+listing.TokenId)] = listing
	}

	// 9. 构建挂单价格信息
	// 从用户的挂单信息中提取价格相关数据，用于后续查询详细的订单信息
	var itemPrice []dao.MultiChainItemPriceInfo
	for _, item := range listingInfos {
		if item.Listing {
			itemPrice = append(itemPrice, dao.MultiChainItemPriceInfo{
				ItemPriceInfo: types.ItemPriceInfo{
					CollectionAddress: item.CollectionAddress,
					TokenID:           item.TokenId,
					Maker:             item.Owner,
					Price:             item.ListPrice,
					OrderStatus:       multi.OrderStatusActive,
				},
				ChainName: chainIDToChainName[item.ChainId],
			})
		}
	}

	// 10. 查询用户挂单的详细订单信息
	// 获取挂单的订单ID、过期时间、盐值等详细信息
	orderIds := make(map[string]multi.Order)
	if len(itemPrice) > 0 {
		orders, err := svcCtx.Dao.QueryMultiChainListingInfo(ctx, itemPrice)
		if err != nil {
			return nil, errors.Wrap(err, "failed on query item order id")
		}

		// 将订单信息存储到map中，key为合约地址+TokenID
		for _, order := range orders {
			orderIds[strings.ToLower(order.CollectionAddress+order.TokenId)] = order
		}
	}

	// 11. 查询NFT图片信息
	// 获取NFT的图片URI，包括原始图片和OSS存储的图片
	itemImages, err := svcCtx.Dao.QueryMultiChainCollectionsItemsImage(ctx, itemInfos)
	if err != nil {
		return nil, errors.Wrap(err, "failed on query item image info")
	}

	// 将图片信息存储到map中，key为合约地址+TokenID
	itemExternals := make(map[string]multi.ItemExternal)
	for _, item := range itemImages {
		itemExternals[strings.ToLower(item.CollectionAddress+item.TokenId)] = item
	}

	// 12. 组装最终结果
	// 将查询到的各种信息整合到每个NFT的数据结构中
	for i := 0; i < len(items); i++ {
		// 设置出价信息（其他用户对该NFT的最高出价）
		// 优先使用Item级别的出价，如果没有则使用Collection级别的出价
		itemKey := dao.MultiChainItemInfo{ItemInfo: types.ItemInfo{CollectionAddress: strings.ToLower(items[i].CollectionAddress), TokenID: items[i].TokenID}, ChainName: chainIDToChainName[items[i].ChainID]}
		collectionKey := types.MultichainCollection{
			CollectionAddress: strings.ToLower(items[i].CollectionAddress),
			Chain:             chainIDToChainName[items[i].ChainID],
		}

		// 检查是否有针对该NFT的特定出价
		bidOrder, hasItemBid := itemsBestBids[itemKey]
		collectionBid, hasCollectionBid := collectionBestBids[collectionKey]

		// 优先使用Item级别的出价，且价格要高于Collection级别的出价
		if hasItemBid && (!hasCollectionBid || bidOrder.Price.GreaterThan(collectionBid.Price)) {
			// 使用Item级别的最高出价
			items[i].BidOrderID = bidOrder.OrderID
			items[i].BidExpireTime = bidOrder.ExpireTime
			items[i].BidPrice = bidOrder.Price
			items[i].BidTime = bidOrder.EventTime
			items[i].BidSalt = bidOrder.Salt
			items[i].BidMaker = bidOrder.Maker
			items[i].BidType = getBidType(bidOrder.OrderType)
			items[i].BidSize = bidOrder.Size
			items[i].BidUnfilled = bidOrder.QuantityRemaining
		} else if hasCollectionBid {
			// 使用Collection级别的最高出价
			items[i].BidOrderID = collectionBid.OrderID
			items[i].BidExpireTime = collectionBid.ExpireTime
			items[i].BidPrice = collectionBid.Price
			items[i].BidTime = collectionBid.EventTime
			items[i].BidSalt = collectionBid.Salt
			items[i].BidMaker = collectionBid.Maker
			items[i].BidType = getBidType(collectionBid.OrderType)
			items[i].BidSize = collectionBid.Size
			items[i].BidUnfilled = collectionBid.QuantityRemaining
		}

		// 设置Collection信息（NFT所属集合的基本信息）
		collection, ok := collectionInfos[strings.ToLower(items[i].CollectionAddress)]
		if ok {
			items[i].CollectionName = collection.Name
			items[i].FloorPrice = collection.FloorPrice
			items[i].CollectionImageURI = collection.ImageUri
			// 如果NFT没有名称，使用集合名称+TokenID作为默认名称
			if items[i].Name == "" {
				items[i].Name = fmt.Sprintf("%s #%s", collection.Name, items[i].TokenID)
			}
		}

		// 设置用户的挂单信息（用户作为卖家的挂单）
		listing, ok := listingInfos[strings.ToLower(items[i].CollectionAddress+items[i].TokenID)]
		if ok {
			items[i].ListPrice = listing.ListPrice    // 挂单价格
			items[i].Listing = listing.Listing        // 是否在售
			items[i].MarketplaceID = listing.MarketID // 市场ID
		}

		// 设置用户挂单的详细订单信息
		order, ok := orderIds[strings.ToLower(items[i].CollectionAddress+items[i].TokenID)]
		if ok {
			items[i].ListOrderID = order.OrderID       // 挂单订单ID
			items[i].ListTime = order.EventTime        // 挂单时间
			items[i].ListExpireTime = order.ExpireTime // 挂单过期时间
			items[i].ListSalt = order.Salt             // 订单盐值
			items[i].ListMaker = order.Maker           // 挂单创建者（用户地址）
		}

		// 设置NFT图片信息
		image, ok := itemExternals[strings.ToLower(items[i].CollectionAddress+items[i].TokenID)]
		if ok {
			// 优先使用OSS存储的图片，如果没有则使用原始图片URI
			if image.IsUploadedOss {
				items[i].ImageURI = image.OssUri
			} else {
				items[i].ImageURI = image.ImageUri
			}
		}
	}

	return &types.UserItemsResp{
		Result: items,
		Count:  count,
	}, nil
}

// GetMultiChainUserListings 获取用户在多条链上的挂单信息
func GetMultiChainUserListings(ctx context.Context, svcCtx *svc.ServerCtx, chainID []int, chain []string, userAddrs []string, contractAddrs []string, page, pageSize int) (*types.UserListingsResp, error) {
	var result []types.Listing
	// 1. 查询用户挂单Item基本信息
	items, count, err := svcCtx.Dao.QueryMultiChainUserListingItemInfos(ctx, chain, userAddrs, contractAddrs, page, pageSize)
	if err != nil {
		return nil, errors.Wrap(err, "failed on get user items info")
	}

	// 如果没有挂单,直接返回空结果
	if count == 0 {
		return &types.UserListingsResp{
			Count: count,
		}, nil
	}

	// 2. 构建chainID到chain name的映射
	chainIDToChainName := make(map[int]string)
	for i, _ := range chainID {
		chainIDToChainName[chainID[i]] = chain[i]
	}

	// 3. 获取用户地址
	var userAddr string
	if len(userAddrs) == 0 {
		userAddr = ""
	} else {
		userAddr = userAddrs[0]
	}

	// 4. 准备查询参数
	var collectionAddrs [][]string                          // Collection地址和链名称对
	var itemInfos []dao.MultiChainItemInfo                  // Item信息
	var chainCollections = make(map[string][]string)        // 按链分组的Collection地址
	var multichainItems = make(map[string][]types.ItemInfo) // 按链分组的Item信息

	// 遍历Item,构建查询参数
	for _, item := range items {
		collectionAddrs = append(collectionAddrs, []string{strings.ToLower(item.CollectionAddress), chainIDToChainName[item.ChainID]})
		itemInfos = append(itemInfos, dao.MultiChainItemInfo{
			ItemInfo: types.ItemInfo{
				CollectionAddress: item.CollectionAddress,
				TokenID:           item.TokenID,
			},
			ChainName: chainIDToChainName[item.ChainID],
		})

		chainCollections[strings.ToLower(chainIDToChainName[item.ChainID])] = append(chainCollections[strings.ToLower(chainIDToChainName[item.ChainID])], item.CollectionAddress)
		multichainItems[chainIDToChainName[item.ChainID]] = append(multichainItems[chainIDToChainName[item.ChainID]], types.ItemInfo{
			CollectionAddress: item.CollectionAddress,
			TokenID:           item.TokenID,
		})
	}

	// 5. 记录Item最近成本
	itemLastCost := make(map[dao.MultiChainItemInfo]decimal.Decimal)

	// 6. 并发查询Collection最高出价信息
	collectionBestBids := make(map[types.MultichainCollection]multi.Order)
	var wg sync.WaitGroup
	var mu sync.Mutex
	var queryErr error
	for chain, collections := range chainCollections {
		wg.Add(1)
		go func(chainName string, collectionArray []string) {
			defer wg.Done()
			bestBids, err := svcCtx.Dao.QueryCollectionsBestBid(ctx, chainName, userAddr, collectionArray)
			if err != nil {
				queryErr = errors.Wrap(err, "failed on query collections best bids")
				return
			}
			mu.Lock()
			defer mu.Unlock()
			for _, bestBid := range bestBids {
				collectionBestBids[types.MultichainCollection{
					CollectionAddress: strings.ToLower(bestBid.CollectionAddress),
					Chain:             chainName,
				}] = *bestBid
			}
		}(chain, collections)
	}
	wg.Wait()
	if queryErr != nil {
		return nil, errors.Wrap(err, "failed on query collection bids")
	}

	// 7. 并发查询Item最高出价信息
	itemsBestBids := make(map[dao.MultiChainItemInfo]multi.Order)
	for chain, items := range multichainItems {
		wg.Add(1)
		go func(chainName string, itemInfos []types.ItemInfo) {
			defer wg.Done()
			bids, err := svcCtx.Dao.QueryItemsBestBids(ctx, chainName, userAddr, itemInfos)
			if err != nil {
				queryErr = errors.Wrap(err, "failed on query items best bids")
				return
			}

			mu.Lock()
			defer mu.Unlock()
			for _, bid := range bids {
				order, ok := itemsBestBids[dao.MultiChainItemInfo{ItemInfo: types.ItemInfo{CollectionAddress: strings.ToLower(bid.CollectionAddress), TokenID: bid.TokenId}, ChainName: chainName}]
				if !ok {
					itemsBestBids[dao.MultiChainItemInfo{ItemInfo: types.ItemInfo{CollectionAddress: strings.ToLower(bid.CollectionAddress), TokenID: bid.TokenId}, ChainName: chainName}] = bid
					continue
				}
				if bid.Price.GreaterThan(order.Price) {
					itemsBestBids[dao.MultiChainItemInfo{ItemInfo: types.ItemInfo{CollectionAddress: strings.ToLower(bid.CollectionAddress), TokenID: bid.TokenId}, ChainName: chainName}] = bid
				}
			}
		}(chain, items)
	}
	wg.Wait()
	if queryErr != nil {
		return nil, errors.Wrap(err, "failed on query items best bids")
	}

	// 8. 查询Collection基本信息
	collections, err := svcCtx.Dao.QueryMultiChainCollectionsInfo(ctx, collectionAddrs)
	if err != nil {
		return nil, errors.Wrap(err, "failed on query collections info")
	}

	collectionInfos := make(map[string]multi.Collection)
	for _, collection := range collections {
		collectionInfos[strings.ToLower(collection.Address)] = collection
	}

	// 9. 查询用户Item挂单信息
	listings, err := svcCtx.Dao.QueryMultiChainUserItemsExpireListInfo(ctx, userAddrs, itemInfos)
	if err != nil {
		return nil, errors.Wrap(err, "failed on query item list info")
	}

	listingInfos := make(map[string]*dao.CollectionItem)
	for _, listing := range listings {
		listingInfos[strings.ToLower(listing.CollectionAddress+listing.TokenId)] = listing
	}

	// 10. 查询挂单订单信息
	var itemPrice []dao.MultiChainItemPriceInfo
	for _, item := range listingInfos {
		if item.Listing {
			itemPrice = append(itemPrice, dao.MultiChainItemPriceInfo{
				ItemPriceInfo: types.ItemPriceInfo{
					CollectionAddress: item.CollectionAddress,
					TokenID:           item.TokenId,
					Maker:             item.Owner,
					Price:             item.ListPrice,
					OrderStatus:       item.OrderStatus,
				},
				ChainName: chainIDToChainName[item.ChainId],
			})
		}
	}

	orderIds := make(map[string]multi.Order)
	if len(itemPrice) > 0 {
		orders, err := svcCtx.Dao.QueryMultiChainListingInfo(ctx, itemPrice)
		if err != nil {
			return nil, errors.Wrap(err, "failed on query item order id")
		}

		for _, order := range orders {
			orderIds[strings.ToLower(order.CollectionAddress+order.TokenId)] = order
		}
	}

	// 11. 查询Item图片信息
	itemImages, err := svcCtx.Dao.QueryMultiChainCollectionsItemsImage(ctx, itemInfos)
	if err != nil {
		return nil, errors.Wrap(err, "failed on query item image info")
	}

	itemExternals := make(map[string]multi.ItemExternal)
	for _, item := range itemImages {
		itemExternals[strings.ToLower(item.CollectionAddress+item.TokenId)] = item
	}

	// 12. 组装最终结果
	for i := 0; i < len(items); i++ {
		var resultlisting types.Listing
		listing, ok := listingInfos[strings.ToLower(items[i].CollectionAddress+items[i].TokenID)]
		if ok {
			resultlisting.ListPrice = listing.ListPrice
			resultlisting.MarketplaceID = listing.MarketID
		} else {
			count--
			continue
		}

		resultlisting.ChainID = items[i].ChainID
		resultlisting.CollectionAddress = items[i].CollectionAddress
		resultlisting.TokenID = items[i].TokenID
		resultlisting.LastCostPrice = itemLastCost[dao.MultiChainItemInfo{
			ItemInfo: types.ItemInfo{
				CollectionAddress: items[i].CollectionAddress,
				TokenID:           items[i].TokenID,
			},
			ChainName: chainIDToChainName[items[i].ChainID],
		}]

		// 设置出价信息 - 优先使用Item出价,如果没有则使用Collection出价
		bidOrder, ok := itemsBestBids[dao.MultiChainItemInfo{ItemInfo: types.ItemInfo{CollectionAddress: strings.ToLower(items[i].CollectionAddress), TokenID: items[i].TokenID}, ChainName: chainIDToChainName[items[i].ChainID]}]
		if ok {
			if bidOrder.Price.GreaterThan(collectionBestBids[types.MultichainCollection{
				CollectionAddress: strings.ToLower(items[i].CollectionAddress),
				Chain:             chainIDToChainName[items[i].ChainID],
			}].Price) {
				resultlisting.BidOrderID = bidOrder.OrderID
				resultlisting.BidExpireTime = bidOrder.ExpireTime
				resultlisting.BidPrice = bidOrder.Price
				resultlisting.BidTime = bidOrder.EventTime
				resultlisting.BidSalt = bidOrder.Salt
				resultlisting.BidMaker = bidOrder.Maker
				resultlisting.BidType = getBidType(bidOrder.OrderType)
				resultlisting.BidSize = bidOrder.Size
				resultlisting.BidUnfilled = bidOrder.QuantityRemaining
			}
		} else {
			bidOrder, ok := collectionBestBids[types.MultichainCollection{
				CollectionAddress: strings.ToLower(items[i].CollectionAddress),
				Chain:             chainIDToChainName[items[i].ChainID],
			}]

			if ok {
				resultlisting.BidOrderID = bidOrder.OrderID
				resultlisting.BidExpireTime = bidOrder.ExpireTime
				resultlisting.BidPrice = bidOrder.Price
				resultlisting.BidTime = bidOrder.EventTime
				resultlisting.BidSalt = bidOrder.Salt
				resultlisting.BidMaker = bidOrder.Maker
				resultlisting.BidType = getBidType(bidOrder.OrderType)
				resultlisting.BidSize = bidOrder.Size
				resultlisting.BidUnfilled = bidOrder.QuantityRemaining
			}
		}

		// 设置Collection信息
		collection, ok := collectionInfos[strings.ToLower(items[i].CollectionAddress)]
		if ok {
			resultlisting.CollectionName = collection.Name
			if resultlisting.Name == "" {
				resultlisting.Name = fmt.Sprintf("%s #%s", collection.Name, items[i].TokenID)
			}
			resultlisting.FloorPrice = collection.FloorPrice
		}

		// 设置订单信息
		order, ok := orderIds[strings.ToLower(items[i].CollectionAddress+items[i].TokenID)]
		if ok {
			resultlisting.ListOrderID = order.OrderID
			resultlisting.ListExpireTime = order.ExpireTime
			resultlisting.ListMaker = order.Maker
			resultlisting.ListSalt = order.Salt
		}

		// 设置图片信息
		image, ok := itemExternals[strings.ToLower(items[i].CollectionAddress+items[i].TokenID)]
		if ok {
			if image.IsUploadedOss {
				resultlisting.ImageURI = image.OssUri
			} else {
				resultlisting.ImageURI = image.ImageUri
			}
		}
		result = append(result, resultlisting)
	}

	return &types.UserListingsResp{
		Count:  count,
		Result: result,
	}, nil
}

type multiOrder struct {
	multi.Order
	chainID   int
	chainName string
}

// GetMultiChainUserBids 获取用户在多条链上的出价信息
//
// 功能说明：
// 1. 查询用户作为买家的所有出价记录（用户想要购买的NFT）
// 2. 支持多链查询，包括Item级别和Collection级别的出价
// 3. 合并相同条件的出价信息，提供详细的出价历史
// 4. 返回Collection基本信息和出价详情
//
// 与GetMultiChainUserItems的区别：
// - GetMultiChainUserItems：查询用户拥有的NFT + 其他人对这些NFT的出价
// - GetMultiChainUserBids：查询用户自己的出价记录（用户想要购买的NFT）
//
// 参数:
// - ctx: 上下文
// - svcCtx: 服务上下文
// - chainID: 链ID列表
// - chainNames: 链名称列表
// - userAddrs: 用户地址列表（查询这些用户的出价）
// - contractAddrs: 合约地址列表（可选，筛选特定合约）
// - page: 页码
// - pageSize: 每页大小
// 返回:
// - *types.UserBidsResp: 用户出价信息响应
// - error: 错误信息
func GetMultiChainUserBids(ctx context.Context, svcCtx *svc.ServerCtx, chainID []int, chainNames []string, userAddrs []string, contractAddrs []string, page, pageSize int) (*types.UserBidsResp, error) {
	// 1. 遍历每条链，查询用户的出价信息
	// 从Activity表中查询用户作为买家的所有出价记录
	var totalBids []multiOrder
	for i, chain := range chainNames {
		// 查询指定链上用户的出价记录
		orders, err := svcCtx.Dao.QueryUserBids(ctx, chain, userAddrs, contractAddrs)
		if err != nil {
			return nil, errors.Wrap(err, "failed on get user bids info")
		}

		// 将每条链的出价信息添加到总出价列表中，并标记链信息
		var tmpBids []multiOrder
		for j := 0; j < len(orders); j++ {
			tmpBids = append(tmpBids, multiOrder{
				Order:     orders[j],
				chainID:   chainID[i],
				chainName: chain,
			})
		}
		totalBids = append(totalBids, tmpBids...)
	}

	// 2. 构建出价信息映射和Collection地址映射
	// 将相同条件的出价进行合并，并收集需要查询的Collection地址
	bidsMap := make(map[string]types.UserBid)
	bidCollections := make(map[string][]string)
	for _, bid := range totalBids {
		// 按链名称分组Collection地址，用于后续批量查询Collection信息
		if collections, ok := bidCollections[bid.chainName]; ok {
			bidCollections[bid.chainName] = append(collections, strings.ToLower(bid.CollectionAddress))
		} else {
			bidCollections[bid.chainName] = []string{strings.ToLower(bid.CollectionAddress)}
		}

		// 构建唯一key，用于合并相同条件的出价信息
		// key包含：合约地址+TokenID+价格+市场ID+过期时间+订单类型
		// 相同key的出价会被合并到一个UserBid中，但保留所有BidInfo详情
		key := strings.ToLower(bid.CollectionAddress) + bid.TokenId + bid.Price.String() + fmt.Sprintf("%d", bid.MarketplaceId) + fmt.Sprintf("%d", bid.ExpireTime) + fmt.Sprintf("%d", bid.OrderType)
		userBid, ok := bidsMap[key]
		if !ok {
			// 如果key不存在，创建新的出价信息
			bidsMap[key] = types.UserBid{
				ChainID:           bid.chainID,
				CollectionAddress: strings.ToLower(bid.CollectionAddress),
				TokenID:           bid.TokenId,
				BidPrice:          bid.Price,
				MarketplaceID:     bid.MarketplaceId,
				ExpireTime:        bid.ExpireTime,
				BidType:           getBidType(bid.OrderType),
				OrderSize:         bid.QuantityRemaining,
				BidInfos: []types.BidInfo{
					{
						BidOrderID:    bid.OrderID,
						BidTime:       bid.EventTime,
						BidExpireTime: bid.ExpireTime,
						BidPrice:      bid.Price,
						BidSalt:       bid.Salt,
						BidSize:       bid.Size,
						BidUnfilled:   bid.QuantityRemaining,
					},
				},
			}
			continue
		}

		// 如果key存在，更新出价信息（合并相同条件的多个出价）
		userBid.OrderSize += bid.QuantityRemaining
		userBid.BidInfos = append(userBid.BidInfos, types.BidInfo{
			BidOrderID:    bid.OrderID,
			BidTime:       bid.EventTime,
			BidExpireTime: bid.ExpireTime,
			BidPrice:      bid.Price,
			BidSalt:       bid.Salt,
			BidSize:       bid.Size,
			BidUnfilled:   bid.QuantityRemaining,
		})
		bidsMap[key] = userBid
	}

	// 3. 查询Collection基本信息
	// 批量查询用户出价涉及的所有Collection的基本信息
	collectionInfos := make(map[string]multi.Collection)
	for chain, collections := range bidCollections {
		// 去重后查询Collection信息，避免重复查询
		cs, err := svcCtx.Dao.QueryCollectionsInfo(ctx, chain, removeRepeatedElement(collections))
		if err != nil {
			return nil, errors.Wrap(err, "failed on get collections info")
		}

		// 将Collection信息存储到map中，key为chainID:address格式
		for _, c := range cs {
			collectionInfos[fmt.Sprintf("%d:%s", c.ChainId, strings.ToLower(c.Address))] = c
		}
	}

	// 4. 组装最终结果
	// 将出价信息与Collection信息合并，构建完整的用户出价数据
	var results []types.UserBid
	for _, userBid := range bidsMap {
		// 设置Collection名称和图片信息
		collectionKey := fmt.Sprintf("%d:%s", userBid.ChainID, strings.ToLower(userBid.CollectionAddress))
		if c, ok := collectionInfos[collectionKey]; ok {
			userBid.CollectionName = c.Name
			userBid.ImageURI = c.ImageUri
		}

		results = append(results, userBid)
	}

	// 5. 按过期时间降序排序
	// 最新的出价排在前面，方便用户查看最近的出价活动
	sort.SliceStable(results, func(i, j int) bool {
		return results[i].ExpireTime > (results[j].ExpireTime)
	})

	return &types.UserBidsResp{
		Count:  len(bidsMap),
		Result: results,
	}, nil
}

func removeRepeatedElement(arr []string) (newArr []string) {
	newArr = make([]string, 0)
	for i := 0; i < len(arr); i++ {
		repeat := false
		for j := i + 1; j < len(arr); j++ {
			if arr[i] == arr[j] {
				repeat = true
				break
			}
		}
		if !repeat && arr[i] != "" {
			newArr = append(newArr, arr[i])
		}
	}
	return
}
