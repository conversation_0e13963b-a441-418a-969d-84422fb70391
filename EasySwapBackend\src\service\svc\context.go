package svc

import (
	"context"

	"github.com/ProjectsTask/EasySwapBase/chain/nftchainservice"
	"github.com/ProjectsTask/EasySwapBase/logger/xzap"
	"github.com/ProjectsTask/EasySwapBase/stores/gdb"
	"github.com/ProjectsTask/EasySwapBase/stores/xkv"
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/kv"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"gorm.io/gorm"

	"github.com/ProjectsTask/EasySwapBackend/src/config"
	"github.com/ProjectsTask/EasySwapBackend/src/dao"
)

type ServerCtx struct {
	C  *config.Config
	DB *gorm.DB
	//ImageMgr image.ImageManager
	Dao      *dao.Dao
	KvStore  *xkv.Store
	RankKey  string
	NodeSrvs map[int64]*nftchainservice.Service
}

func NewServiceContext(c *config.Config) (*ServerCtx, error) {
	var err error
	//imageMgr, err = image.NewManager(c.ImageCfg)
	//if err != nil {
	//	return nil, errors.Wrap(err, "failed on create image manager")
	//}

	// Log
	_, err = xzap.SetUp(c.Log) // 初始化日志
	if err != nil {
		return nil, err
	}

	var kvConf kv.KvConf
	//查询redis配置 创建redis连接 将多个redis连接加入到kvConf，形成redis集群
	for _, con := range c.Kv.Redis {
		kvConf = append(kvConf, cache.NodeConf{
			RedisConf: redis.RedisConf{
				Host: con.Host,
				Type: con.Type,
				Pass: con.Pass,
			},
			Weight: 1,
		})
	}

	// 返回redis链接信息
	store := xkv.NewStore(kvConf)

	// 连接mysql
	db, err := gdb.NewDB(&c.DB)
	if err != nil {
		return nil, err
	}

	// 创建一个以chainID为key的服务映射表，用于存储每个链的nft服务实例
	// 支持查询多条链的NFT集合，每条链都有自己的ctx上下文，链id查询不同链服务
	nodeSrvs := make(map[int64]*nftchainservice.Service)
	for _, supported := range c.ChainSupported { //遍历配置表
		nodeSrvs[int64(supported.ChainID)], err = nftchainservice.New(context.Background(), supported.Endpoint, supported.Name, supported.ChainID,
			c.MetadataParse.NameTags, c.MetadataParse.ImageTags, c.MetadataParse.AttributesTags,
			c.MetadataParse.TraitNameTags, c.MetadataParse.TraitValueTags)

		if err != nil {
			return nil, errors.Wrap(err, "failed on start onchain sync service")
		}
	}
	// 创建dao 实例 访问数据库和缓存
	dao := dao.New(context.Background(), db, store)
	serverCtx := NewServerCtx( // 创建服务实例集合
		WithDB(db),
		WithKv(store),
		//WithImageMgr(imageMgr),
		WithDao(dao),
	)
	serverCtx.C = c // 将配置表加入服务实例集合

	serverCtx.NodeSrvs = nodeSrvs // 将链服务加入服务实例集合

	return serverCtx, nil
}
