package router

import (
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	"github.com/ProjectsTask/EasySwapBackend/src/api/middleware"

	"github.com/ProjectsTask/EasySwapBackend/src/service/svc"
)

func NewRouter(svcCtx *svc.ServerCtx) *gin.Engine {
	gin.ForceConsoleColor()
	gin.SetMode(gin.ReleaseMode)
	r := gin.New() // 新建一个gin引擎实例
	// 使用恢复中间件 捕获http请求中处理过程中的panic,不能捕获其他的地方
	// 如果不捕获，就会阻塞进程，程序奔溃
	r.Use(middleware.RecoverMiddleware())
	r.Use(middleware.RLog()) // 使用日志中间件

	r.Use(cors.New(cors.Config{ // 使用cors中间件
		AllowAllOrigins: true, // 允许所有域名访问
		// 允许的请求方法
		AllowMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"},
		// 允许的请求头 特别是 Authorization、Token 用于身份验证
		AllowHeaders: []string{"Origin", "Content-Length", "Content-Type", "X-CSRF-Token", "Authorization", "AccessToken", "Token"},
		// 允许前端JavaScript读取的响应头
		ExposeHeaders: []string{"Content-Length", "Content-Type", "Access-Control-Allow-Origin", "Access-Control-Allow-Headers", "X-GW-Error-Code", "X-GW-Error-Message"},
		// // 允许发送Cookie和认证信息
		AllowCredentials: true,
		// 预检请求缓存1小时，即缓存1小时
		MaxAge: 1 * time.Hour,
	}))
	loadV1(r, svcCtx) // 加载v1路由

	return r
}
