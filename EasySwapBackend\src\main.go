package main

import (
	"flag"
	_ "net/http/pprof"

	"github.com/ProjectsTask/EasySwapBackend/src/api/router"
	"github.com/ProjectsTask/EasySwapBackend/src/app"
	"github.com/ProjectsTask/EasySwapBackend/src/config"
	"github.com/ProjectsTask/EasySwapBackend/src/service/svc"
)

const (
	// port       = ":9000"
	repoRoot          = ""
	defaultConfigPath = "./config/config.toml"
)

func main() {
	conf := flag.String("conf", defaultConfigPath, "conf file path")
	flag.Parse()                            // 解析命令行参数
	c, err := config.UnmarshalConfig(*conf) //解析配置文件
	if err != nil {
		panic(err)
	}

	for _, chain := range c.ChainSupported { // 检查chain_suffix配置
		if chain.ChainID == 0 || chain.Name == "" {
			panic("invalid chain_suffix config")
		}
	}

	serverCtx, err := svc.NewServiceContext(c) // 创建redis、mysql、多链、日志服务
	if err != nil {
		panic(err)
	}
	// Initialize router
	r := router.NewRouter(serverCtx)             // 创建路由
	app, err := app.NewPlatform(c, r, serverCtx) // 将配置、路由、服务上下文传入 集合创建一个平台
	if err != nil {
		panic(err)
	}
	app.Start() // 启动平台
}
