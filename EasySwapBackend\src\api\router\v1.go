package router

import (
	"github.com/gin-gonic/gin"

	"github.com/ProjectsTask/EasySwapBackend/src/api/middleware"
	v1 "github.com/ProjectsTask/EasySwapBackend/src/api/v1"
	"github.com/ProjectsTask/EasySwapBackend/src/service/svc"
)

func loadV1(r *gin.Engine, svcCtx *svc.ServerCtx) {
	apiV1 := r.Group("/api/v1") //路由分组

	user := apiV1.Group("/user") // 用户认证相关接口
	{
		user.GET("/:address/login-message", v1.GetLoginMessageHandler(svcCtx)) // 生成用户登录时需要签名的消息内容（Web3钱包签名登录）
		user.POST("/login", v1.UserLoginHandler(svcCtx))                       // 用户登录验证（验证钱包签名，生成JWT token）
		user.GET("/:address/sig-status", v1.GetSigStatusHandler(svcCtx))       // 获取用户的签名状态（检查是否已登录）
	}

	collections := apiV1.Group("/collections") //获取Collection（NFT集合信息）信息
	{
		// ==================== 集合级别接口 ====================
		collections.GET("/:address", v1.CollectionDetailHandler(svcCtx))                                   // 获取指定NFT集合的详细信息（名称、描述、地板价、交易量等）
		collections.GET("/:address/bids", v1.CollectionBidsHandler(svcCtx))                                // 获取指定集合的所有出价信息（Collection级别的出价，按价格分组显示）
		collections.GET("/:address/items", v1.CollectionItemsHandler(svcCtx))                              // 获取指定集合下的所有NFT列表（支持筛选、排序、分页）
		collections.GET("/ranking", middleware.CacheApi(svcCtx.KvStore, 60), v1.TopRankingHandler(svcCtx)) // 获取NFT集合排行榜（按交易量、地板价等排序）

		// ==================== 单个NFT接口 ====================
		collections.GET("/:address/:token_id", v1.ItemDetailHandler(svcCtx))                                                  // 获取指定NFT的详细信息（价格、属性、历史等）
		collections.GET("/:address/:token_id/bids", v1.CollectionItemBidsHandler(svcCtx))                                     // 获取指定NFT的所有出价信息（Item级别的出价列表）
		collections.GET("/:address/:token_id/traits", v1.ItemTraitsHandler(svcCtx))                                           // 获取指定NFT的属性信息（稀有度、特征等）
		collections.GET("/:address/:token_id/image", middleware.CacheApi(svcCtx.KvStore, 60), v1.GetItemImageHandler(svcCtx)) // 获取指定NFT的图片（支持缓存）
		collections.GET("/:address/:token_id/owner", v1.ItemOwnerHandler(svcCtx))                                             // 获取指定NFT的当前持有者信息
		collections.POST("/:address/:token_id/metadata", v1.ItemMetadataRefreshHandler(svcCtx))                               // 刷新指定NFT的元数据（手动更新）

		// ==================== 集合分析接口 ====================
		collections.GET("/:address/top-trait", v1.ItemTopTraitPriceHandler(svcCtx)) // 获取集合中各属性的最高价格信息（属性价值分析）
		collections.GET("/:address/history-sales", v1.HistorySalesHandler(svcCtx))  // 获取集合的历史销售数据（价格趋势分析）
	}

	activities := apiV1.Group("/activities") // 市场活动记录接口
	{
		activities.GET("", v1.ActivityMultiChainHandler(svcCtx)) // 获取多链NFT市场活动记录（铸造、转账、销售、挂单、出价等所有链上活动）
	}

	portfolio := apiV1.Group("/portfolio") // 用户投资组合接口（用户资产管理）
	{
		portfolio.GET("/collections", v1.UserMultiChainCollectionsHandler(svcCtx)) // 获取用户拥有的NFT集合信息（按集合分组显示用户资产）
		portfolio.GET("/items", v1.UserMultiChainItemsHandler(svcCtx))             // 获取用户拥有的所有NFT详细信息（包括挂单状态、收到的出价等）
		portfolio.GET("/listings", v1.UserMultiChainListingsHandler(svcCtx))       // 获取用户的所有挂单信息（用户作为卖家的挂单列表）
		portfolio.GET("/bids", v1.UserMultiChainBidsHandler(svcCtx))               // 获取用户的所有出价信息（用户作为买家的出价列表）
	}

	orders := apiV1.Group("/bid-orders") // 订单查询接口
	{
		orders.GET("", v1.OrderInfosHandler(svcCtx)) // 批量查询出价订单的详细信息（根据订单ID查询订单状态、价格等）
	}
}
